import httpx
import json
import base64
import os
import logging
import time
import asyncio

# 导入函数调用模块
try:
    from src.agent import function_calling_agent
    ENABLE_FUNCTION_CALLING = True
except ImportError:
    ENABLE_FUNCTION_CALLING = False
    logging.warning("函数调用模块导入失败，函数调用功能已禁用")

# 全局变量存储access_token和过期时间
_access_token = None
_token_expires_at = 0

async def get_access_token(config, logger):
    """
    获取访问令牌

    Args:
        config: 配置对象
        logger: 日志对象

    Returns:
        str: 访问令牌，如果获取失败返回None
    """
    global _access_token, _token_expires_at

    try:
        # 检查当前token是否还有效（提前5分钟刷新）
        current_time = time.time()
        if _access_token and current_time < (_token_expires_at - 300):
            logger.info("使用缓存的access_token")
            return _access_token

        # 获取配置信息
        base_url = config.get("JIUAN_API", "BASE_URL")
        token_path = config.get("JIUAN_API", "TOKEN_PATH")
        client_id = config.get("JIUAN_API", "CLIENT_ID")
        client_secret = config.get("JIUAN_API", "CLIENT_SECRET")
        verify_ssl = config.getboolean("JIUAN_API", "VERIFY_SSL", fallback=False)

        # 构建token获取URL
        token_url = f"{base_url}{token_path}?client_id={client_id}&client_secret={client_secret}"

        logger.info(f"准备获取access_token: {base_url}{token_path}")

        # 发送请求获取token
        async with httpx.AsyncClient(verify=verify_ssl) as client:
            response = await client.get(token_url, timeout=30)

            if response.status_code != 200:
                logger.error(f"获取access_token失败: {response.status_code}, {response.text}")
                return None

            token_data = response.json()
            logger.info(f"Token响应: {json.dumps(token_data, ensure_ascii=False)[:100]}...")

            # 提取access_token
            access_token = token_data.get("access_token")
            expires_in = token_data.get("expires_in", 3600)  # 默认1小时

            if not access_token:
                logger.error("响应中未找到access_token")
                return None

            # 更新全局变量
            _access_token = access_token
            _token_expires_at = current_time + expires_in

            logger.info(f"成功获取access_token，有效期: {expires_in}秒")
            return access_token

    except Exception as e:
        logger.error(f"获取access_token时出错: {str(e)}")
        return None

async def chat_with_model(chat_data, config, logger):
    """
    与大模型进行对话

    Args:
        chat_data: 聊天数据，包含完整的API请求格式
        config: 配置对象
        logger: 日志对象

    Returns:
        dict: 大模型的响应结果
    """
    try:
        # 从chat_data中提取用户消息用于函数调用检查
        user_message = ""
        if isinstance(chat_data, dict) and "object" in chat_data:
            messages = chat_data.get("object", {}).get("messages", [])
            for msg in reversed(messages):
                if msg.get("role") == "user":
                    user_message = msg.get("content", "")
                    break
        elif isinstance(chat_data, str):
            # 兼容旧格式，使用配置文件中的默认参数
            user_message = chat_data
            chat_data = {
                "object": {
                    "id": "chat_session_001",
                    "model": config.get("JIUAN_API", "CHAT_DEFAULT_MODEL", fallback="deepseek-r1-full"),
                    "messages": [{"role": "user", "content": user_message}],
                    "stream": False,
                    "max_tokens": config.getint("JIUAN_API", "CHAT_DEFAULT_MAX_TOKENS", fallback=2048),
                    "temperature": config.getfloat("JIUAN_API", "CHAT_DEFAULT_TEMPERATURE", fallback=0.7),
                    "top_p": config.getfloat("JIUAN_API", "CHAT_DEFAULT_TOP_P", fallback=0.9),
                    "frequency_penalty": config.getfloat("JIUAN_API", "CHAT_DEFAULT_FREQUENCY_PENALTY", fallback=0.0),
                    "presence_penalty": config.getfloat("JIUAN_API", "CHAT_DEFAULT_PRESENCE_PENALTY", fallback=0.0),
                    "stop": None
                }
            }

        # 检查是否是API函数调用请求
        if ENABLE_FUNCTION_CALLING and user_message:
            # 使用LangChain Agent处理查询
            logger.info(f"尝试使用LangChain Agent处理查询: {user_message}")
            function_result = function_calling_agent(user_message)
            if function_result:
                # 如果有结果，直接返回
                logger.info(f"LangChain Agent处理成功，返回结果")
                return {
                    "status": "success",
                    "data": {
                        "result": function_result,
                        "is_function_call": True
                    }
                }
            else:
                logger.info(f"LangChain Agent未返回结果，切换到常规对话模式")
        else:
            logger.info("函数调用功能未启用或无用户消息，使用常规对话模式")

        # 获取access_token
        access_token = await get_access_token(config, logger)
        if not access_token:
            return {
                "status": "error",
                "message": "无法获取访问令牌，请检查配置"
            }

        # 获取配置信息 - 从JIUAN_API部分获取
        base_url = config.get("JIUAN_API", "BASE_URL")
        chat_path = config.get("JIUAN_API", "CHAT_PATH")
        client_id = config.get("JIUAN_API", "CLIENT_ID")

        # 组合成完整URL，包含client_id和access_token参数
        url = f"{base_url}{chat_path}?client_id={client_id}&access_token={access_token}"
        content_type = config.get("JIUAN_API", "CHAT_CONTENT_TYPE", fallback="application/json")
        timeout = config.getint("JIUAN_API", "CHAT_TIMEOUT", fallback=120)
        verify_ssl = config.getboolean("JIUAN_API", "VERIFY_SSL", fallback=False)

        logger.info(f"准备调用久安大模型对话接口: {base_url}{chat_path}")
        logger.info(f"用户消息: {user_message}")

        # 准备请求头
        headers = {
            "Content-Type": content_type
        }

        # 使用传入的chat_data作为请求体
        payload = chat_data

        # 发送请求
        async with httpx.AsyncClient(verify=verify_ssl) as client:
            logger.info("发送请求到久安大模型对话接口")
            response = await client.post(
                url,
                json=payload,
                headers=headers,
                timeout=timeout
            )

            # 检查响应状态
            if response.status_code != 200:
                logger.error(f"对话接口返回错误: {response.status_code}, {response.text}")
                return {
                    "status": "error",
                    "message": f"接口调用失败: {response.status_code}",
                    "data": response.text if response.text else "无响应内容"
                }

            # 解析响应内容
            response_data = response.json()
            logger.info(f"对话接口返回成功: {json.dumps(response_data, ensure_ascii=False)[:100]}...")

            # 返回结果
            return {
                "status": "success",
                "data": response_data
            }

    except Exception as e:
        logger.error(f"调用对话接口时出错: {str(e)}")
        return {
            "status": "error",
            "message": f"调用对话接口时出错: {str(e)}"
        }

async def analyze_image(file_path, query, config, logger, history=None):
    """
    分析图片内容

    Args:
        file_path: 图片文件路径
        query: 用户查询内容
        config: 配置对象
        logger: 日志对象
        history: 对话历史记录，默认为空列表

    Returns:
        dict: 图片分析结果
    """
    try:
        # 检查文件是否存在
        if not os.path.exists(file_path):
            logger.error(f"图片文件不存在: {file_path}")
            return {"status": "error", "message": "图片文件不存在"}

        # 获取配置信息 - 从JIUAN_API部分获取
        base_url = config.get("JIUAN_API", "BASE_URL")
        image_path = config.get("JIUAN_API", "IMAGE_PATH")
        # 组合成完整URL
        url = f"{base_url}{image_path}"
        content_type = config.get("JIUAN_API", "IMAGE_CONTENT_TYPE", fallback="application/json")
        timeout = config.getint("JIUAN_API", "IMAGE_TIMEOUT", fallback=120)
        token = config.get("JIUAN_API", "TOKEN", fallback="")
        verify_ssl = config.getboolean("JIUAN_API", "VERIFY_SSL", fallback=False)

        logger.info(f"准备调用图片分析接口: {url}")
        logger.info(f"图片文件: {file_path}, 查询: {query}")

        # 读取图片文件并转换为base64
        with open(file_path, "rb") as image_file:
            image_content = image_file.read()
            image_base64 = base64.b64encode(image_content).decode('utf-8')

        logger.info(f"图片转换为base64成功，长度: {len(image_base64)}")

        # 准备请求头
        headers = {
            "Content-Type": content_type,
            "Authorization": f"Bearer {token}" if token else ""
        }

        # 如果没有提供history，使用空列表
        if history is None:
            history = []

        # 准备请求体
        # 根据API文档要求的字段格式
        payload = {
            "text": query,
            "history": history,
            "image": image_base64
        }

        # 发送请求
        async with httpx.AsyncClient(verify=verify_ssl) as client:
            logger.info("发送请求到图片分析接口")
            response = await client.post(
                url,
                json=payload,
                headers=headers,
                timeout=timeout
            )

            # 检查响应状态
            if response.status_code != 200:
                logger.error(f"图片分析接口返回错误: {response.status_code}, {response.text}")
                return {
                    "status": "error",
                    "message": f"接口调用失败: {response.status_code}",
                    "data": response.text if response.text else "无响应内容"
                }

            # 解析响应内容
            response_data = response.json()
            logger.info(f"图片分析接口返回成功: {json.dumps(response_data, ensure_ascii=False)[:100]}...")

            # 返回结果
            return {
                "status": "success",
                "data": response_data
            }

    except Exception as e:
        logger.error(f"调用图片分析接口时出错: {str(e)}")
        return {
            "status": "error",
            "message": f"调用图片分析接口时出错: {str(e)}"
        }

async def upload_video_for_analysis(file_path, query, history, config, logger):
    """
    上传视频进行分析，返回任务ID

    Args:
        file_path: 视频文件路径
        query: 用户查询内容
        history: 对话历史列表
        config: 配置对象
        logger: 日志对象

    Returns:
        dict: 包含任务ID的响应结果
    """
    try:
        # 检查文件是否存在
        if not os.path.exists(file_path):
            logger.error(f"视频文件不存在: {file_path}")
            return {"status": "error", "message": "视频文件不存在"}

        # 获取配置信息 - 从JIUAN_API部分获取
        base_url = config.get("JIUAN_API", "BASE_URL")
        video_path = config.get("JIUAN_API", "VIDEO_PATH")
        # 组合成完整URL
        url = f"{base_url}{video_path}"
        content_type = config.get("JIUAN_API", "VIDEO_CONTENT_TYPE", fallback="application/json")
        timeout = config.getint("JIUAN_API", "VIDEO_TIMEOUT", fallback=120)
        token = config.get("JIUAN_API", "TOKEN", fallback="")
        verify_ssl = config.getboolean("JIUAN_API", "VERIFY_SSL", fallback=False)

        logger.info(f"准备调用视频上传分析接口: {url}")
        logger.info(f"视频文件: {file_path}, 查询: {query}")
        logger.info(f"对话历史长度: {len(history) if history else 0}")

        # 读取视频文件并转换为base64
        with open(file_path, "rb") as video_file:
            video_content = video_file.read()
            video_base64 = base64.b64encode(video_content).decode('utf-8')

        logger.info(f"视频转换为base64成功，长度: {len(video_base64)}")

        # 准备请求头
        headers = {
            "Content-Type": content_type,
            "Authorization": f"Bearer {token}" if token else ""
        }

        # 准备请求体
        # 根据API文档要求更新payload格式
        payload = {
            "text": query,
            "history": history if history else [],
            "video": video_base64
        }

        # 发送请求
        async with httpx.AsyncClient(verify=verify_ssl) as client:
            logger.info("发送请求到视频上传分析接口")
            response = await client.post(
                url,
                json=payload,
                headers=headers,
                timeout=timeout
            )

            # 检查响应状态
            if response.status_code != 200:
                logger.error(f"视频上传分析接口返回错误: {response.status_code}, {response.text}")
                return {
                    "status": "error",
                    "message": f"接口调用失败: {response.status_code}",
                    "data": response.text if response.text else "无响应内容"
                }

            # 解析响应内容
            response_data = response.json()
            logger.info(f"视频上传分析接口返回成功: {json.dumps(response_data, ensure_ascii=False)[:100]}...")

            # 提取任务ID
            task_id = None
            if response_data.get("data"):
                task_id = response_data["data"]
            elif response_data.get("task_id"):
                task_id = response_data["task_id"]
            elif response_data.get("id"):
                task_id = response_data["id"]

            if not task_id:
                logger.error("未能从响应中获取任务ID")
                return {
                    "status": "error",
                    "message": "上传成功但未获取到任务ID",
                    "data": response_data
                }

            # 返回结果
            return {
                "status": "success",
                "task_id": task_id,
                "message": "视频上传成功，正在分析中...",
                "data": response_data
            }

    except Exception as e:
        logger.error(f"调用视频上传分析接口时出错: {str(e)}")
        return {
            "status": "error",
            "message": f"调用视频上传分析接口时出错: {str(e)}"
        }

async def get_video_analysis_result(task_id, config, logger):
    """
    根据任务ID获取视频分析结果

    Args:
        task_id: 视频分析任务ID
        config: 配置对象
        logger: 日志对象

    Returns:
        dict: 视频分析结果
    """
    try:
        # 获取配置信息 - 从JIUAN_API部分获取
        base_url = config.get("JIUAN_API", "BASE_URL")
        video_result_path = config.get("JIUAN_API", "VIDEO_RESULT_PATH")
        # 组合成完整URL
        url = f"{base_url}{video_result_path}"
        content_type = config.get("JIUAN_API", "VIDEO_CONTENT_TYPE", fallback="application/json")
        timeout = config.getint("JIUAN_API", "VIDEO_TIMEOUT", fallback=120)
        token = config.get("JIUAN_API", "TOKEN", fallback="")
        verify_ssl = config.getboolean("JIUAN_API", "VERIFY_SSL", fallback=False)

        logger.info(f"准备查询视频分析结果: {url}")
        logger.info(f"任务ID: {task_id}")

        # 准备请求头
        headers = {
            "Content-Type": content_type,
            "Authorization": f"Bearer {token}" if token else ""
        }

        # 准备请求体 - 根据查询结果接口格式
        payload = {
            "task_id": task_id
        }

        # 发送请求
        async with httpx.AsyncClient(verify=verify_ssl) as client:
            logger.info("发送请求到视频结果查询接口")
            response = await client.post(
                url,
                json=payload,
                headers=headers,
                timeout=timeout
            )

            # 检查响应状态
            if response.status_code != 200:
                logger.error(f"视频结果查询接口返回错误: {response.status_code}, {response.text}")
                return {
                    "status": "error",
                    "message": f"接口调用失败: {response.status_code}",
                    "data": response.text if response.text else "无响应内容"
                }

            # 解析响应内容
            response_data = response.json()
            logger.info(f"视频结果查询接口返回: {json.dumps(response_data, ensure_ascii=False)[:100]}...")

            # 返回结果
            return {
                "status": "success",
                "data": response_data
            }

    except Exception as e:
        logger.error(f"查询视频分析结果时出错: {str(e)}")
        return {
            "status": "error",
            "message": f"查询视频分析结果时出错: {str(e)}"
        }

async def analyze_video_with_polling(file_path, query, history, config, logger, max_wait_time=600, poll_interval=10):
    """
    分析视频内容（包含上传和轮询获取结果的完整流程）

    Args:
        file_path: 视频文件路径
        query: 用户查询内容
        history: 对话历史列表
        config: 配置对象
        logger: 日志对象
        max_wait_time: 最大等待时间（秒，默认10分钟）
        poll_interval: 轮询间隔（秒，默认10秒）

    Returns:
        dict: 视频分析结果
    """
    try:
        # 第一步：上传视频获取任务ID
        upload_result = await upload_video_for_analysis(file_path, query, history, config, logger)
        if upload_result["status"] != "success":
            return upload_result

        task_id = upload_result["task_id"]
        logger.info(f"视频上传成功，任务ID: {task_id}")

        # 第二步：智能轮询获取分析结果
        start_time = time.time()
        consecutive_failures = 0
        max_consecutive_failures = 3

        while True:
            current_time = time.time()
            elapsed_time = current_time - start_time

            # 检查是否超时
            if elapsed_time > max_wait_time:
                logger.warning(f"视频分析超时，任务ID: {task_id}, 耗时: {elapsed_time:.2f}秒")
                return {
                    "status": "timeout",
                    "message": f"视频分析超时（{max_wait_time//60}分钟），任务可能仍在后台处理中",
                    "task_id": task_id,
                    "elapsed_time": elapsed_time
                }

            try:
                # 查询分析结果
                result = await get_video_analysis_result(task_id, config, logger)

                if result["status"] == "success" and result.get("data"):
                    consecutive_failures = 0  # 重置失败计数

                    # 更智能的状态判断
                    data = result["data"]

                    # 检查各种可能的完成状态
                    if (data.get("status") == "completed" or
                        data.get("status") == "success" or
                        data.get("result") or
                        data.get("response") or
                        data.get("msg")):

                        logger.info(f"视频分析完成，任务ID: {task_id}, 耗时: {elapsed_time:.2f}秒")
                        return {
                            "status": "success",
                            "data": data,
                            "task_id": task_id,
                            "elapsed_time": elapsed_time
                        }

                    # 检查是否还在处理中
                    elif (data.get("status") == "processing" or
                          data.get("status") == "pending" or
                          data.get("status") == "running"):
                        logger.info(f"视频仍在分析中，任务ID: {task_id}, 已耗时: {elapsed_time:.2f}秒")

                    # 检查是否失败
                    elif (data.get("status") == "failed" or
                          data.get("status") == "error"):
                        logger.error(f"视频分析失败，任务ID: {task_id}")
                        return {
                            "status": "error",
                            "message": "视频分析失败",
                            "data": data,
                            "task_id": task_id,
                            "elapsed_time": elapsed_time
                        }

                else:
                    # 查询接口本身失败
                    consecutive_failures += 1
                    logger.warning(f"查询视频分析状态失败，任务ID: {task_id}, 连续失败次数: {consecutive_failures}")

                    # 如果连续失败太多次，可能是接口问题
                    if consecutive_failures >= max_consecutive_failures:
                        logger.error(f"连续查询失败{max_consecutive_failures}次，可能是接口异常")
                        return {
                            "status": "error",
                            "message": "查询分析状态连续失败，可能是服务异常",
                            "task_id": task_id,
                            "elapsed_time": elapsed_time
                        }

            except Exception as query_error:
                consecutive_failures += 1
                logger.error(f"查询视频分析状态异常，任务ID: {task_id}, 错误: {str(query_error)}")

                if consecutive_failures >= max_consecutive_failures:
                    return {
                        "status": "error",
                        "message": f"查询分析状态异常: {str(query_error)}",
                        "task_id": task_id,
                        "elapsed_time": elapsed_time
                    }

            # 动态调整轮询间隔（越久轮询越慢）
            if elapsed_time < 60:  # 前1分钟，每5秒查询一次
                current_poll_interval = 5
            elif elapsed_time < 300:  # 前5分钟，每10秒查询一次
                current_poll_interval = 10
            else:  # 5分钟后，每30秒查询一次
                current_poll_interval = 30

            logger.info(f"等待{current_poll_interval}秒后再次查询，任务ID: {task_id}")
            await asyncio.sleep(current_poll_interval)

    except Exception as e:
        logger.error(f"视频分析流程出错: {str(e)}")
        return {
            "status": "error",
            "message": f"视频分析流程出错: {str(e)}"
        }

# 为了向后兼容，保留原来的函数名
async def analyze_video(file_path, query, history, config, logger):
    """
    分析视频内容（向后兼容的入口函数）
    """
    return await analyze_video_with_polling(file_path, query, history, config, logger)

async def emergency_knowledge_qa(user_message, config, logger):
    """
    应急行业知识问答

    Args:
        user_message: 用户输入的消息
        config: 配置对象
        logger: 日志对象

    Returns:
        dict: 应急知识问答的响应结果
    """
    try:
        # 检查是否是API函数调用请求
        if ENABLE_FUNCTION_CALLING:
            # 使用LangChain Agent处理查询
            logger.info(f"尝试使用LangChain Agent处理应急知识查询: {user_message}")
            function_result = function_calling_agent(user_message)
            if function_result:
                # 如果有结果，直接返回
                logger.info(f"LangChain Agent处理成功，返回结果")
                return {
                    "status": "success",
                    "data": {
                        "result": function_result,
                        "is_function_call": True
                    }
                }
            else:
                logger.info(f"LangChain Agent未返回结果，切换到应急知识问答模式")
        else:
            logger.info("函数调用功能未启用，使用应急知识问答模式")

        # 获取配置信息 - 从JIUAN_API部分获取
        base_url = config.get("JIUAN_API", "BASE_URL")
        emergency_path = config.get("JIUAN_API", "EMERGENCY_KNOWLEDGE_PATH")
        # 组合成完整URL
        url = f"{base_url}{emergency_path}"
        content_type = config.get("JIUAN_API", "EMERGENCY_KNOWLEDGE_CONTENT_TYPE", fallback="application/json")
        timeout = config.getint("JIUAN_API", "EMERGENCY_KNOWLEDGE_TIMEOUT", fallback=120)
        token = config.get("JIUAN_API", "TOKEN", fallback="")
        verify_ssl = config.getboolean("JIUAN_API", "VERIFY_SSL", fallback=False)

        logger.info(f"准备调用久安大模型应急知识问答接口: {url}")
        logger.info(f"用户消息: {user_message}")

        # 准备请求头
        headers = {
            "Content-Type": content_type,
            "Authorization": f"Bearer {token}" if token else ""
        }

        # 准备请求体 - 针对应急行业知识问答的特殊格式
        payload = {
            "question": user_message,
            "domain": "emergency",  # 指定应急行业领域
            "model": "emergency-knowledge-qa",
            "context": {
                "industry": "emergency_management",
                "type": "knowledge_qa"
            }
        }

        # 发送请求
        async with httpx.AsyncClient(verify=verify_ssl) as client:
            logger.info("发送请求到久安大模型应急知识问答接口")
            response = await client.post(
                url,
                json=payload,
                headers=headers,
                timeout=timeout
            )

            # 检查响应状态
            if response.status_code != 200:
                logger.error(f"应急知识问答接口返回错误: {response.status_code}, {response.text}")
                return {
                    "status": "error",
                    "message": f"接口调用失败: {response.status_code}",
                    "data": response.text if response.text else "无响应内容"
                }

            # 解析响应内容
            response_data = response.json()
            logger.info(f"应急知识问答接口返回成功: {json.dumps(response_data, ensure_ascii=False)[:100]}...")

            # 返回结果
            return {
                "status": "success",
                "data": response_data
            }

    except Exception as e:
        logger.error(f"调用应急知识问答接口时出错: {str(e)}")
        return {
            "status": "error",
            "message": f"调用应急知识问答接口时出错: {str(e)}"
        }

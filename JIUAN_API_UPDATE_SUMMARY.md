# 久安API更新总结

## 更新概述
根据API文档要求，更新了久安聊天接口的认证机制，从直接使用token改为OAuth2.0认证流程。

## 主要更改

### 1. 配置文件更新 (config.ini)
- **BASE_URL**: 更新为 `https://172.28.48.186:8310`
- **新增OAuth配置**:
  - `CLIENT_ID = af51852d19cf43d4884b009f2fb25c93`
  - `CLIENT_SECRET = uZw8vqg!`
  - `TOKEN_PATH = /oauth/token`
- **CHAT_PATH**: 更新为 `/service/api/api/ai_apaas/v1/app/conversation/runs`

### 2. 后端API更新 (src/jiuan_api.py)

#### 新增功能
- **get_access_token()函数**: 实现OAuth2.0 token获取
  - 支持token缓存和自动刷新
  - 提前5分钟刷新token避免过期
  - 错误处理和日志记录

#### 更新功能
- **chat_with_model()函数**: 
  - 集成token获取流程
  - 更新URL构建逻辑，包含client_id和access_token参数
  - 移除旧的Bearer token认证方式

### 3. API端点 (main.py)
- **@app.post("/jiuan_chat/")**: 保持不变，继续支持现有的请求格式
- 后端自动处理token获取和API调用

### 4. 前端页面 (static/jiuan_model.html)
- 无需更改，继续使用现有的请求格式
- 高级设置面板继续工作
- 支持多种对话模式（文本、图片、视频、应急知识问答）

## API调用流程

### 新的认证流程
1. **获取Token**: 
   ```
   GET https://172.28.48.186:8310/oauth/token?client_id={client_id}&client_secret={client_secret}
   ```

2. **调用API**: 
   ```
   POST https://172.28.48.186:8310/service/api/api/ai_apaas/v1/app/conversation/runs?client_id={client_id}&access_token={access_token}
   ```

### 请求格式
前端继续使用现有格式：
```json
{
  "object": {
    "id": "chat_session_001",
    "model": "deepseek-r1-full",
    "messages": [
      {
        "role": "user",
        "content": "用户消息"
      }
    ],
    "stream": false,
    "max_tokens": 2048,
    "temperature": 0.7,
    "top_p": 0.9,
    "frequency_penalty": 0.0,
    "presence_penalty": 0.0,
    "stop": null
  }
}
```

## 兼容性
- **向后兼容**: 支持简单格式和完整格式的请求
- **前端无感知**: 前端代码无需修改
- **配置驱动**: 所有认证信息通过配置文件管理

## 测试
- 创建了测试脚本 `test_jiuan_api.py` 用于验证功能
- 测试token获取和聊天功能

## 注意事项
1. **SSL证书**: 配置中设置 `VERIFY_SSL = false` 以处理自签名证书
2. **网络连接**: 确保服务器可以访问 `172.28.48.186:8310`
3. **Token管理**: 系统自动处理token缓存和刷新
4. **错误处理**: 完善的错误处理和日志记录

## 部署建议
1. 更新配置文件中的认证信息
2. 重启应用服务
3. 测试API连接性
4. 监控日志确保token获取正常
